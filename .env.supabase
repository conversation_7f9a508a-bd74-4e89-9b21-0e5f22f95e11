# Supabase Configuration
SUPABASE_DB_PASSWORD=your-super-secret-and-long-postgres-password
SUPABASE_JWT_SECRET=your-super-secret-jwt-token-with-at-least-32-characters-long
SUPABASE_JWT_EXPIRY=3600

# API Configuration
SUPABASE_API_EXTERNAL_URL=http://localhost:54321
SUPABASE_SITE_URL=http://localhost:3000
SUPABASE_URI_ALLOW_LIST=http://localhost:3000,http://localhost:8080

# Auth Configuration
SUPABASE_DISABLE_SIGNUP=false
SUPABASE_EXTERNAL_EMAIL_ENABLED=true
SUPABASE_MAILER_AUTOCONFIRM=true

# SMTP Configuration (using Inbucket for development)
SUPABASE_SMTP_ADMIN_EMAIL=<EMAIL>
SUPABASE_SMTP_HOST=supabase-inbucket
SUPABASE_SMTP_PORT=2500
SUPABASE_SMTP_USER=fake_mail_user
SUPABASE_SMTP_PASS=fake_mail_password
SUPABASE_SMTP_SENDER_NAME=Cortexa

# URL Paths
SUPABASE_MAILER_URLPATHS_INVITE=/auth/v1/verify
SUPABASE_MAILER_URLPATHS_CONFIRMATION=/auth/v1/verify
SUPABASE_MAILER_URLPATHS_RECOVERY=/auth/v1/verify
SUPABASE_MAILER_URLPATHS_EMAIL_CHANGE=/auth/v1/verify

# Phone/SMS Configuration
SUPABASE_EXTERNAL_PHONE_ENABLED=false
SUPABASE_SMS_AUTOCONFIRM=false

# Database Schemas
SUPABASE_DB_SCHEMAS=public

# Keys (these are default development keys - change in production)
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU

# Image Proxy
IMGPROXY_ENABLE_WEBP_DETECTION=true
