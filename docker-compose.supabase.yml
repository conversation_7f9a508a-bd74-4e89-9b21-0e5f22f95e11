version: '3.8'

services:
  # Supabase Database (minimal setup for auth only)
  supabase-db:
    image: supabase/postgres:*********
    container_name: cortexa-supabase-db
    restart: unless-stopped
    ports:
      - "54322:5432"
    environment:
      POSTGRES_HOST: /var/run/postgresql
      PGPORT: 5432
      POSTGRES_PORT: 5432
      POSTGRES_DB: postgres
      POSTGRES_USER: supabase_admin
      POSTGRES_PASSWORD: ${SUPABASE_DB_PASSWORD:-your-super-secret-and-long-postgres-password}
      JWT_SECRET: ${SUPABASE_JWT_SECRET:-your-super-secret-jwt-token-with-at-least-32-characters-long}
      JWT_EXP: ${SUPABASE_JWT_EXPIRY:-3600}
    volumes:
      - supabase_db_data:/var/lib/postgresql/data
    command:
      - postgres
      - -c
      - config_file=/etc/postgresql/postgresql.conf
      - -c
      - log_min_messages=fatal
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U supabase_admin -d postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - cortexa-network

  # Supabase Auth
  supabase-auth:
    image: supabase/gotrue:v2.158.1
    container_name: cortexa-supabase-auth
    depends_on:
      supabase-db:
        condition: service_healthy
    restart: unless-stopped
    environment:
      GOTRUE_API_HOST: 0.0.0.0
      GOTRUE_API_PORT: 9999
      API_EXTERNAL_URL: ${SUPABASE_API_EXTERNAL_URL:-http://localhost:54321}
      
      GOTRUE_DB_DRIVER: postgres
      GOTRUE_DB_DATABASE_URL: postgres://supabase_auth_admin:${SUPABASE_DB_PASSWORD:-your-super-secret-and-long-postgres-password}@supabase-db:5432/postgres
      
      GOTRUE_SITE_URL: ${SUPABASE_SITE_URL:-http://localhost:3000}
      GOTRUE_URI_ALLOW_LIST: ${SUPABASE_URI_ALLOW_LIST:-http://localhost:3000,http://localhost:8080}
      GOTRUE_DISABLE_SIGNUP: ${SUPABASE_DISABLE_SIGNUP:-false}
      GOTRUE_JWT_SECRET: ${SUPABASE_JWT_SECRET:-your-super-secret-jwt-token-with-at-least-32-characters-long}
      GOTRUE_JWT_EXP: ${SUPABASE_JWT_EXPIRY:-3600}
      GOTRUE_JWT_DEFAULT_GROUP_NAME: authenticated
      GOTRUE_JWT_ADMIN_ROLES: service_role
      GOTRUE_JWT_AUD: authenticated
      
      GOTRUE_EXTERNAL_EMAIL_ENABLED: ${SUPABASE_EXTERNAL_EMAIL_ENABLED:-true}
      GOTRUE_MAILER_AUTOCONFIRM: ${SUPABASE_MAILER_AUTOCONFIRM:-false}
      GOTRUE_SMTP_ADMIN_EMAIL: ${SUPABASE_SMTP_ADMIN_EMAIL:-<EMAIL>}
      GOTRUE_SMTP_HOST: ${SUPABASE_SMTP_HOST:-supabase-inbucket}
      GOTRUE_SMTP_PORT: ${SUPABASE_SMTP_PORT:-2500}
      GOTRUE_SMTP_USER: ${SUPABASE_SMTP_USER:-fake_mail_user}
      GOTRUE_SMTP_PASS: ${SUPABASE_SMTP_PASS:-fake_mail_password}
      GOTRUE_SMTP_SENDER_NAME: ${SUPABASE_SMTP_SENDER_NAME:-fake_sender}
      
      GOTRUE_MAILER_URLPATHS_INVITE: ${SUPABASE_MAILER_URLPATHS_INVITE:-/auth/v1/verify}
      GOTRUE_MAILER_URLPATHS_CONFIRMATION: ${SUPABASE_MAILER_URLPATHS_CONFIRMATION:-/auth/v1/verify}
      GOTRUE_MAILER_URLPATHS_RECOVERY: ${SUPABASE_MAILER_URLPATHS_RECOVERY:-/auth/v1/verify}
      GOTRUE_MAILER_URLPATHS_EMAIL_CHANGE: ${SUPABASE_MAILER_URLPATHS_EMAIL_CHANGE:-/auth/v1/verify}
      
      GOTRUE_EXTERNAL_PHONE_ENABLED: ${SUPABASE_EXTERNAL_PHONE_ENABLED:-false}
      GOTRUE_SMS_AUTOCONFIRM: ${SUPABASE_SMS_AUTOCONFIRM:-false}
    networks:
      - cortexa-network

  # Supabase REST API (minimal for auth management)
  supabase-rest:
    image: postgrest/postgrest:v12.2.0
    container_name: cortexa-supabase-rest
    depends_on:
      supabase-db:
        condition: service_healthy
    restart: unless-stopped
    environment:
      PGRST_DB_URI: postgres://supabase_api_admin:${SUPABASE_DB_PASSWORD:-your-super-secret-and-long-postgres-password}@supabase-db:5432/postgres
      PGRST_DB_SCHEMAS: ${SUPABASE_DB_SCHEMAS:-public,auth}
      PGRST_DB_ANON_ROLE: anon
      PGRST_JWT_SECRET: ${SUPABASE_JWT_SECRET:-your-super-secret-jwt-token-with-at-least-32-characters-long}
      PGRST_DB_USE_LEGACY_GUCS: "false"
      PGRST_APP_SETTINGS_JWT_SECRET: ${SUPABASE_JWT_SECRET:-your-super-secret-jwt-token-with-at-least-32-characters-long}
      PGRST_APP_SETTINGS_JWT_EXP: ${SUPABASE_JWT_EXPIRY:-3600}
    command:
      - postgrest
    networks:
      - cortexa-network

  # Supabase Studio (Management UI)
  supabase-studio:
    image: supabase/studio:20240326-5e5586d
    container_name: cortexa-supabase-studio
    restart: unless-stopped
    ports:
      - "54323:3000"
    depends_on:
      supabase-kong:
        condition: service_started
    environment:
      STUDIO_PG_META_URL: http://supabase-kong:8000/pg
      POSTGRES_PASSWORD: ${SUPABASE_DB_PASSWORD:-your-super-secret-and-long-postgres-password}
      DEFAULT_ORGANIZATION_NAME: Cortexa
      DEFAULT_PROJECT_NAME: Cortexa Auth
      SUPABASE_URL: http://supabase-kong:8000
      SUPABASE_REST_URL: http://localhost:54321/rest/v1/
      SUPABASE_ANON_KEY: ${SUPABASE_ANON_KEY:-eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0}
      SUPABASE_SERVICE_KEY: ${SUPABASE_SERVICE_ROLE_KEY:-eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU}
      LOGFLARE_API_KEY: your-super-secret-and-long-logflare-key
      LOGFLARE_URL: http://analytics:4000
      NEXT_PUBLIC_ENABLE_LOGS: true
    networks:
      - cortexa-network

  # Inbucket for email testing
  supabase-inbucket:
    image: inbucket/inbucket:stable
    container_name: cortexa-supabase-inbucket
    restart: unless-stopped
    ports:
      - "54324:9000"  # Web interface
      - "54325:2500"  # SMTP
      - "54326:1100"  # POP3
    environment:
      INBUCKET_WEB_ADDR: 0.0.0.0:9000
      INBUCKET_POP3_ADDR: 0.0.0.0:1100
      INBUCKET_SMTP_ADDR: 0.0.0.0:2500
    networks:
      - cortexa-network

  # Kong API Gateway (minimal setup for auth only)
  supabase-kong:
    image: kong:2.8-alpine
    container_name: cortexa-supabase-kong
    restart: unless-stopped
    ports:
      - "54321:8000/tcp"
      - "54331:8443/tcp"
    depends_on:
      supabase-auth:
        condition: service_started
      supabase-rest:
        condition: service_started
    environment:
      KONG_DATABASE: "off"
      KONG_DECLARATIVE_CONFIG: /var/lib/kong/kong.yml
      KONG_DNS_ORDER: LAST,A,CNAME
      KONG_PLUGINS: request-transformer,cors,key-auth,acl,basic-auth
      KONG_NGINX_PROXY_PROXY_BUFFER_SIZE: 160k
      KONG_NGINX_PROXY_PROXY_BUFFERS: 64 160k
    volumes:
      - ./supabase/kong.yml:/var/lib/kong/kong.yml:ro
    networks:
      - cortexa-network

volumes:
  supabase_db_data:

networks:
  cortexa-network:
    external: true
